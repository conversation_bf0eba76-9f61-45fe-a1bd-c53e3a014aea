<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>两列头像丝滑滚动展示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            background: #222;
            color: #fff;
            padding: 20px;
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #4CAF50;
        }
        
        .scroll-container {
            display: flex;
            width: 100%;
            max-width: 800px;
            height: 500px;
            margin: 0 auto;
            overflow: hidden;
            position: relative;
            background: #333;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 0 20px rgba(0,0,0,0.5);
        }
        
        .column {
            flex: 1;
            display: flex;
            flex-direction: column;
            position: relative;
        }
        
        .left-column {
            margin-right: 15px;
        }
        
        .avatar-item {
            height: 80px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #4CAF50;
            border-radius: 50%;
            color: white;
            font-weight: bold;
            font-size: 18px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            transition: transform 0.3s;
        }
        
        .avatar-item:hover {
            transform: scale(1.1);
            background: #45a049;
        }
        
        /* 现代浏览器CSS动画 */
        @-webkit-keyframes scroll {
            0% { -webkit-transform: translateY(0); }
            100% { -webkit-transform: translateY(-50%); }
        }
        
        @keyframes scroll {
            0% { transform: translateY(0); }
            100% { transform: translateY(-50%); }
        }
        
        .css-animation .column {
            -webkit-animation: scroll 20s linear infinite;
            animation: scroll 20s linear infinite;
        }
        
        .css-animation .column:hover {
            -webkit-animation-play-state: paused;
            animation-play-state: paused;
        }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        button {
            padding: 10px 20px;
            margin: 0 10px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }
        
        button:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <h1>两列头像丝滑滚动展示</h1>
    
    <div class="controls">
        <button id="toggleMode">切换滚动模式</button>
        <button id="toggleSpeed">切换滚动速度</button>
    </div>
    
    <div class="scroll-container" id="scrollContainer">
        <div class="column left-column">
            <div class="avatar-item">头像1</div>
            <div class="avatar-item">头像3</div>
            <div class="avatar-item">头像5</div>
            <div class="avatar-item">头像7</div>
            <div class="avatar-item">头像9</div>
            <div class="avatar-item">头像11</div>
            <div class="avatar-item">头像13</div>
            <div class="avatar-item">头像15</div>
            <!-- 克隆内容用于无缝滚动 -->
            <div class="avatar-item">头像1</div>
            <div class="avatar-item">头像3</div>
            <div class="avatar-item">头像5</div>
            <div class="avatar-item">头像7</div>
            <div class="avatar-item">头像9</div>
            <div class="avatar-item">头像11</div>
            <div class="avatar-item">头像13</div>
            <div class="avatar-item">头像15</div>
        </div>
        
        <div class="column right-column">
            <div class="avatar-item">头像2</div>
            <div class="avatar-item">头像4</div>
            <div class="avatar-item">头像6</div>
            <div class="avatar-item">头像8</div>
            <div class="avatar-item">头像10</div>
            <div class="avatar-item">头像12</div>
            <div class="avatar-item">头像14</div>
            <div class="avatar-item">头像16</div>
            <!-- 克隆内容用于无缝滚动 -->
            <div class="avatar-item">头像2</div>
            <div class="avatar-item">头像4</div>
            <div class="avatar-item">头像6</div>
            <div class="avatar-item">头像8</div>
            <div class="avatar-item">头像10</div>
            <div class="avatar-item">头像12</div>
            <div class="avatar-item">头像14</div>
            <div class="avatar-item">头像16</div>
        </div>
    </div>

    <script>
        // 检测浏览器是否支持CSS动画
        function supportsCssAnimations() {
            var style = document.createElement('div').style;
            return 'animation' in style || 'WebkitAnimation' in style || 'MozAnimation' in style || 'OAnimation' in style || 'msAnimation' in style;
        }
        
        // JavaScript滚动实现
        function jsScrollAnimation(speed) {
            const columns = document.querySelectorAll('.column');
            let animationId;
            
            function animate() {
                columns.forEach(column => {
                    // 获取当前滚动位置
                    let currentPosition = parseInt(column.dataset.position) || 0;
                    
                    // 滚动内容
                    currentPosition -= speed;
                    column.style.transform = 'translateY(' + currentPosition + 'px)';
                    column.dataset.position = currentPosition;
                    
                    // 重置位置实现无缝滚动
                    const columnHeight = column.scrollHeight / 2; // 因为我们克隆了内容
                    if (Math.abs(currentPosition) >= columnHeight) {
                        currentPosition = 0;
                        column.style.transform = 'translateY(0)';
                        column.dataset.position = '0';
                    }
                });
                
                animationId = requestAnimationFrame(animate);
            }
            
            // 停止当前动画
            function stop() {
                cancelAnimationFrame(animationId);
            }
            
            // 初始化位置
            columns.forEach(column => {
                column.style.position = 'relative';
                column.style.transform = 'translateY(0)';
                column.dataset.position = '0';
            });
            
            // 开始动画
            animate();
            
            return {
                stop: stop
            };
        }
        
        // 当前滚动模式和速度
        let currentMode = supportsCssAnimations() ? 'css' : 'js';
        let currentSpeed = 1;
        let jsAnimation = null;
        
        // 初始化滚动
        function initScroll() {
            const container = document.getElementById('scrollContainer');
            
            if (currentMode === 'css') {
                container.classList.add('css-animation');
                if (jsAnimation) {
                    jsAnimation.stop();
                    jsAnimation = null;
                }
            } else {
                container.classList.remove('css-animation');
                jsAnimation = jsScrollAnimation(currentSpeed);
            }
        }
        
        // 切换滚动模式
        document.getElementById('toggleMode').addEventListener('click', function() {
            currentMode = currentMode === 'css' ? 'js' : 'css';
            initScroll();
        });
        
        // 切换滚动速度
        document.getElementById('toggleSpeed').addEventListener('click', function() {
            currentSpeed = currentSpeed === 1 ? 2 : 1;
            if (currentMode === 'js' && jsAnimation) {
                jsAnimation.stop();
                jsAnimation = jsScrollAnimation(currentSpeed);
            }
        });
        
        // 页面加载完成后启动滚动
        if (document.addEventListener) {
            document.addEventListener('DOMContentLoaded', initScroll);
        } else {
            // 兼容IE8及以下
            window.attachEvent('onload', initScroll);
        }
    </script>
</body>
</html>