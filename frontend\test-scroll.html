<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart7 丝滑滚动测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: #222;
            color: #fff;
            padding: 20px;
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #4CAF50;
        }
        
        .ranking-container {
            width: 100%;
            max-width: 400px;
            height: 500px;
            border-radius: 8px;
            padding: 8px;
            box-sizing: border-box;
            position: relative;
            overflow: hidden;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid #5ecbff;
        }

        .scroll-container {
            display: flex;
            width: 100%;
            height: 100%;
            overflow: hidden;
            position: relative;
            z-index: 1;
            gap: 8px;
        }

        .column {
            flex: 1;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .left-column {
            margin-right: 4px;
        }

        .right-column {
            margin-left: 4px;
        }

        /* CSS动画关键帧 */
        @-webkit-keyframes scroll {
            0% { -webkit-transform: translateY(0); }
            100% { -webkit-transform: translateY(-50%); }
        }

        @keyframes scroll {
            0% { transform: translateY(0); }
            100% { transform: translateY(-50%); }
        }

        /* 应用CSS动画 */
        .css-animation .column {
            -webkit-animation: scroll 30s linear infinite;
            animation: scroll 30s linear infinite;
        }

        /* 鼠标悬停时暂停动画 */
        .css-animation .column:hover {
            -webkit-animation-play-state: paused;
            animation-play-state: paused;
        }

        /* 暂停状态 */
        .css-animation.paused .column {
            -webkit-animation-play-state: paused;
            animation-play-state: paused;
        }

        .ranking-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            margin-bottom: 12px;
            min-height: 64px;
            cursor: pointer;
        }

        .ranking-item:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateX(5px);
        }

        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 12px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            position: relative;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .avatar-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #ffffff;
            font-size: 16px;
            font-weight: bold;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .info {
            flex: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .name {
            color: #ffffff;
            font-size: 16px;
            font-weight: 500;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .controls {
            text-align: center;
            margin: 20px 0;
        }

        button {
            padding: 10px 20px;
            margin: 0 10px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }

        button:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <h1>Chart7 代表名单丝滑滚动测试</h1>
    
    <div class="controls">
        <button onclick="toggleAnimation()">开始/停止滚动</button>
        <button onclick="changeSpeed()">切换速度</button>
    </div>
    
    <div class="ranking-container">
        <div class="scroll-container css-animation" id="scrollContainer">
            <!-- 左列 -->
            <div class="column left-column">
                <div class="ranking-item">
                    <div class="avatar">
                        <div class="avatar-placeholder">张</div>
                    </div>
                    <div class="info">
                        <div class="name">张三</div>
                    </div>
                </div>
                <div class="ranking-item">
                    <div class="avatar">
                        <div class="avatar-placeholder">王</div>
                    </div>
                    <div class="info">
                        <div class="name">王五</div>
                    </div>
                </div>
                <div class="ranking-item">
                    <div class="avatar">
                        <div class="avatar-placeholder">赵</div>
                    </div>
                    <div class="info">
                        <div class="name">赵七</div>
                    </div>
                </div>
                <div class="ranking-item">
                    <div class="avatar">
                        <div class="avatar-placeholder">钱</div>
                    </div>
                    <div class="info">
                        <div class="name">钱九</div>
                    </div>
                </div>
                <div class="ranking-item">
                    <div class="avatar">
                        <div class="avatar-placeholder">孙</div>
                    </div>
                    <div class="info">
                        <div class="name">孙十一</div>
                    </div>
                </div>
                <!-- 复制数据实现无缝滚动 -->
                <div class="ranking-item">
                    <div class="avatar">
                        <div class="avatar-placeholder">张</div>
                    </div>
                    <div class="info">
                        <div class="name">张三</div>
                    </div>
                </div>
                <div class="ranking-item">
                    <div class="avatar">
                        <div class="avatar-placeholder">王</div>
                    </div>
                    <div class="info">
                        <div class="name">王五</div>
                    </div>
                </div>
                <div class="ranking-item">
                    <div class="avatar">
                        <div class="avatar-placeholder">赵</div>
                    </div>
                    <div class="info">
                        <div class="name">赵七</div>
                    </div>
                </div>
                <div class="ranking-item">
                    <div class="avatar">
                        <div class="avatar-placeholder">钱</div>
                    </div>
                    <div class="info">
                        <div class="name">钱九</div>
                    </div>
                </div>
                <div class="ranking-item">
                    <div class="avatar">
                        <div class="avatar-placeholder">孙</div>
                    </div>
                    <div class="info">
                        <div class="name">孙十一</div>
                    </div>
                </div>
            </div>
            
            <!-- 右列 -->
            <div class="column right-column">
                <div class="ranking-item">
                    <div class="avatar">
                        <div class="avatar-placeholder">李</div>
                    </div>
                    <div class="info">
                        <div class="name">李四</div>
                    </div>
                </div>
                <div class="ranking-item">
                    <div class="avatar">
                        <div class="avatar-placeholder">周</div>
                    </div>
                    <div class="info">
                        <div class="name">周六</div>
                    </div>
                </div>
                <div class="ranking-item">
                    <div class="avatar">
                        <div class="avatar-placeholder">吴</div>
                    </div>
                    <div class="info">
                        <div class="name">吴八</div>
                    </div>
                </div>
                <div class="ranking-item">
                    <div class="avatar">
                        <div class="avatar-placeholder">郑</div>
                    </div>
                    <div class="info">
                        <div class="name">郑十</div>
                    </div>
                </div>
                <div class="ranking-item">
                    <div class="avatar">
                        <div class="avatar-placeholder">冯</div>
                    </div>
                    <div class="info">
                        <div class="name">冯十二</div>
                    </div>
                </div>
                <!-- 复制数据实现无缝滚动 -->
                <div class="ranking-item">
                    <div class="avatar">
                        <div class="avatar-placeholder">李</div>
                    </div>
                    <div class="info">
                        <div class="name">李四</div>
                    </div>
                </div>
                <div class="ranking-item">
                    <div class="avatar">
                        <div class="avatar-placeholder">周</div>
                    </div>
                    <div class="info">
                        <div class="name">周六</div>
                    </div>
                </div>
                <div class="ranking-item">
                    <div class="avatar">
                        <div class="avatar-placeholder">吴</div>
                    </div>
                    <div class="info">
                        <div class="name">吴八</div>
                    </div>
                </div>
                <div class="ranking-item">
                    <div class="avatar">
                        <div class="avatar-placeholder">郑</div>
                    </div>
                    <div class="info">
                        <div class="name">郑十</div>
                    </div>
                </div>
                <div class="ranking-item">
                    <div class="avatar">
                        <div class="avatar-placeholder">冯</div>
                    </div>
                    <div class="info">
                        <div class="name">冯十二</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let isAnimating = true;
        let currentSpeed = 30; // 30秒
        
        function toggleAnimation() {
            const container = document.getElementById('scrollContainer');
            if (isAnimating) {
                container.classList.remove('css-animation');
                isAnimating = false;
            } else {
                container.classList.add('css-animation');
                isAnimating = true;
            }
        }
        
        function changeSpeed() {
            const container = document.getElementById('scrollContainer');
            const columns = container.querySelectorAll('.column');
            
            // 切换速度：30秒 <-> 15秒
            currentSpeed = currentSpeed === 30 ? 15 : 30;
            
            columns.forEach(column => {
                column.style.animationDuration = currentSpeed + 's';
            });
            
            console.log('滚动速度已切换到:', currentSpeed + '秒');
        }
        
        // 添加鼠标悬停暂停功能
        const container = document.getElementById('scrollContainer');
        container.addEventListener('mouseenter', function() {
            if (isAnimating) {
                this.classList.add('paused');
            }
        });
        
        container.addEventListener('mouseleave', function() {
            this.classList.remove('paused');
        });
        
        // 添加触摸暂停功能
        container.addEventListener('touchstart', function() {
            if (isAnimating) {
                this.classList.add('paused');
            }
        });
        
        container.addEventListener('touchend', function() {
            this.classList.remove('paused');
        });
    </script>
</body>
</html>
