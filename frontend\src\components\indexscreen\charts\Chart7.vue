<template>
  <div class="ranking-container">
    <div
      class="scroll-container"
      ref="scrollContainer"
      @mouseenter="pauseScrollOnHover"
      @mouseleave="resumeScrollOnHover"
      @touchstart="pauseScrollOnTouch"
      @touchend="resumeScrollOnTouch"
    >
      <!-- 左列 -->
      <div class="column left-column" ref="leftColumn">
        <div
          class="ranking-item clickable-representative"
          v-for="(item, index) in leftColumnData"
          :key="`left-${index}`"
          @click="showRepresentativeDetail(item)"
        >
          <div class="avatar">
            <img v-if="hasLoadedAvatar(item)" :src="getAvatarUrl(item)" :alt="item.name" @error="handleUserAvatarError($event, item)" />
            <img v-else-if="shouldShowDefaultAvatar(item)" :src="defaultAvatar" :alt="item.name" @error="handleDefaultAvatarError($event, item)" />
            <div v-else class="avatar-placeholder">
              {{ item.name.charAt(0) }}
            </div>
          </div>
          <div class="info">
            <div class="name">{{ item.name }}</div>
          </div>
        </div>
      </div>

      <!-- 右列 -->
      <div class="column right-column" ref="rightColumn">
        <div
          class="ranking-item clickable-representative"
          v-for="(item, index) in rightColumnData"
          :key="`right-${index}`"
          @click="showRepresentativeDetail(item)"
        >
          <div class="avatar">
            <img v-if="hasLoadedAvatar(item)" :src="getAvatarUrl(item)" :alt="item.name" @error="handleUserAvatarError($event, item)" />
            <img v-else-if="shouldShowDefaultAvatar(item)" :src="defaultAvatar" :alt="item.name" @error="handleDefaultAvatarError($event, item)" />
            <div v-else class="avatar-placeholder">
              {{ item.name.charAt(0) }}
            </div>
          </div>
          <div class="info">
            <div class="name">{{ item.name }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 代表详情弹窗 -->
    <ModalDialog
      v-if="showModal"
      :visible="showModal"
      :title="'代表详细信息'"
      @close="closeModal"
    >
      <div class="representative-detail">
        <div class="detail-header">
          <div class="detail-avatar">
            <img v-if="hasLoadedAvatar(selectedRepresentative)" :src="getAvatarUrl(selectedRepresentative)" :alt="selectedRepresentative.name" @error="handleUserAvatarError($event, selectedRepresentative)" />
            <img v-else-if="shouldShowDefaultAvatar(selectedRepresentative)" :src="defaultAvatar" :alt="selectedRepresentative.name" @error="handleDefaultAvatarError($event, selectedRepresentative)" />
            <div v-else class="avatar-placeholder">
              {{ selectedRepresentative?.name?.charAt(0) || '?' }}
            </div>

          </div>
          <div class="detail-name">{{ selectedRepresentative?.name || '未知' }}</div>
        </div>

        <div class="detail-body">
          <div class="detail-row">
            <span class="label">姓名：</span>
            <span class="value">{{ selectedRepresentative?.name || '未知' }}</span>
          </div>
          <div class="detail-row">
            <span class="label">性别：</span>
            <span class="value">{{ selectedRepresentative?.gender || '未知' }}</span>
          </div>
          <div class="detail-row">
            <span class="label">代表层级：</span>
            <span class="value">{{ getLevelDisplayText(selectedRepresentative?.level) }}</span>
          </div>
          <div class="detail-row">
            <span class="label">所属片区：</span>
            <span class="value">{{ selectedRepresentative?.district || '未知' }}</span>
          </div>
          <div class="detail-row">
            <span class="label">联系方式：</span>
            <span class="value">{{ selectedRepresentative?.mobile_phone || '未提供' }}</span>
          </div>
          <div class="detail-row">
            <span class="label">党派：</span>
            <span class="value">{{ selectedRepresentative?.party || '群众' }}</span>
          </div>
          <div class="detail-row">
            <span class="label">民族：</span>
            <span class="value">{{ selectedRepresentative?.nationality || '未知' }}</span>
          </div>
        </div>
      </div>
    </ModalDialog>
  </div>
</template>

<script>
// 导入默认头像
import defaultAvatarUrl from '@/assets/default_avatar.PNG'
import ModalDialog from '../common/ModalDialog.vue'
import request from '@/api/http/client'
import { getLevelDisplayText } from '@/constants/representative'

export default {
  components: {
    ModalDialog
  },
  props: {
    rankingData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      scrollInterval: null,
      failedUserAvatars: new Set(),
      failedDefaultAvatars: new Set(),
      defaultAvatar: defaultAvatarUrl,
      showModal: false,
      selectedRepresentative: null,
      avatarCache: {}, // 头像缓存 {id: avatarUrl}
      loadingAvatars: new Set(), // 正在加载的头像ID
      loadQueue: [], // 加载队列
      isLoadingAvatar: false, // 是否正在加载头像
    }
  },
  computed: {
    // 左列数据：奇数索引的代表 + 复制数据实现无缝滚动
    leftColumnData() {
      const leftData = this.rankingData.filter((_, index) => index % 2 === 0)
      return [...leftData, ...leftData]
    },

    // 右列数据：偶数索引的代表 + 复制数据实现无缝滚动
    rightColumnData() {
      const rightData = this.rankingData.filter((_, index) => index % 2 === 1)
      return [...rightData, ...rightData]
    }
  },
  watch: {
    rankingData: {
      handler(newData) {
        if (newData && newData.length > 0) {
          // 数据更新时重新加载头像
          this.$nextTick(() => {
            this.startLoadingAvatars()
          })
        }
      },
      immediate: false
    }
  },
  mounted() {
    this.$nextTick(() => {
      setTimeout(() => {
        this.startAutoScroll()
      }, 1000)
      // 开始加载头像
      this.startLoadingAvatars()
    })
  },
  beforeUnmount() {
    this.stopAutoScroll()
    if (this.resumeTimeout) {
      clearTimeout(this.resumeTimeout)
    }
    // 清理blob URLs
    Object.values(this.avatarCache).forEach(url => {
      if (url.startsWith('blob:')) {
        URL.revokeObjectURL(url)
      }
    })
  },
  methods: {
    // 检查是否已加载头像
    hasLoadedAvatar(item) {
      return this.avatarCache[item.id] && !this.failedUserAvatars.has(item.name)
    },

    // 获取头像URL
    getAvatarUrl(item) {
      return this.avatarCache[item.id] || ''
    },

    // 检查是否应该显示默认头像
    shouldShowDefaultAvatar(item) {
      return !this.failedDefaultAvatars.has(item.name)
    },

    // 开始加载头像
    startLoadingAvatars() {
      // 构建加载队列
      this.loadQueue = this.rankingData
        .filter(item => item.has_avatar && !this.avatarCache[item.id])
        .map(item => item.id)

      console.log(`开始加载 ${this.loadQueue.length} 个头像`)
      this.processAvatarQueue()
    },

    // 处理头像加载队列
    async processAvatarQueue() {
      if (this.isLoadingAvatar || this.loadQueue.length === 0) {
        return
      }

      this.isLoadingAvatar = true
      const representativeId = this.loadQueue.shift()

      try {
        await this.loadSingleAvatar(representativeId)
      } catch (error) {
        console.error(`加载头像失败 ID:${representativeId}`, error)
      }

      this.isLoadingAvatar = false

      // 继续处理下一个
      if (this.loadQueue.length > 0) {
        setTimeout(() => {
          this.processAvatarQueue()
        }, 100) // 100ms间隔，避免请求过于频繁
      }
    },

    // 加载单个头像
    async loadSingleAvatar(representativeId) {
      if (this.loadingAvatars.has(representativeId)) {
        return // 避免重复加载
      }

      this.loadingAvatars.add(representativeId)

      try {
        const response = await request({
          url: `/bigscreen/avatar/${representativeId}/`,
          method: 'get',
          responseType: 'blob',
          showLoading: false
        })

        // 将blob转换为URL
        const avatarUrl = URL.createObjectURL(response.data)
        this.avatarCache[representativeId] = avatarUrl

        console.log(`头像加载成功 ID:${representativeId}`)
      } catch (error) {
        console.log(`头像加载失败 ID:${representativeId}`, error)
        // 标记为失败，避免重复尝试
        const representative = this.rankingData.find(item => item.id === representativeId)
        if (representative) {
          this.failedUserAvatars.add(representative.name)
        }
      } finally {
        this.loadingAvatars.delete(representativeId)
      }
    },

    // 处理用户头像加载错误
    handleUserAvatarError(event, item) {
      console.log(`用户头像显示失败: ${item.name}`)
      this.failedUserAvatars.add(item.name)
      event.target.style.display = 'none'
      this.$forceUpdate()
    },

    // 处理默认头像加载错误
    handleDefaultAvatarError(event, item) {
      console.log(`默认头像加载失败: ${item.name}`)
      this.failedDefaultAvatars.add(item.name)
      event.target.style.display = 'none'
      this.$forceUpdate()
    },
    startAutoScroll() {
      this.stopAutoScroll()

      // 使用CSS动画实现丝滑滚动
      const container = this.$refs.scrollContainer
      if (container && this.rankingData.length > 0) {
        // 根据数据数量动态计算滚动时间
        // 每个代表项显示3秒，确保所有代表都能被看到
        const itemDisplayTime = 3 // 每个代表显示3秒
        const totalItems = Math.max(this.leftColumnData.length / 2, this.rightColumnData.length / 2) // 原始数据长度
        const animationDuration = totalItems * itemDisplayTime

        // 设置动画时长
        const columns = container.querySelectorAll('.column')
        columns.forEach(column => {
          column.style.animationDuration = animationDuration + 's'
        })

        container.classList.add('css-animation')
        console.log(`滚动动画启动: ${totalItems}个代表, 动画时长${animationDuration}秒`)
      }
    },
    stopAutoScroll() {
      const container = this.$refs.scrollContainer
      if (container) {
        container.classList.remove('css-animation')
      }
      if (this.scrollInterval) {
        clearInterval(this.scrollInterval)
        this.scrollInterval = null
      }
    },

    showRepresentativeDetail(representative) {
      // 暂停自动滚动
      this.stopAutoScroll()
      // 显示详情弹窗
      this.selectedRepresentative = representative
      this.showModal = true
    },

    closeModal() {
      this.showModal = false
      this.selectedRepresentative = null
      // 恢复自动滚动
      this.startAutoScroll()
    },

    handleDetailAvatarError(event) {
      event.target.style.display = 'none'
    },
    pauseAutoScroll() {
      this.isManualScrolling = true
      this.stopAutoScroll()
    },
    resumeAutoScroll() {
      // 清除之前的恢复定时器
      if (this.resumeTimeout) {
        clearTimeout(this.resumeTimeout)
      }

      // 2秒后恢复自动滚动
      this.resumeTimeout = setTimeout(() => {
        this.isManualScrolling = false
        this.startAutoScroll()
      }, 2000)
    },
    handleScroll() {
      // 检测是否为手动滚动
      this.pauseAutoScroll()
      this.resumeAutoScroll()
    },
    handleWheel() {
      // 鼠标滚轮事件
      this.pauseAutoScroll()
      this.resumeAutoScroll()
    },
    handleTouch() {
      // 触摸开始事件
      this.pauseAutoScroll()
    },
    handleTouchEnd() {
      // 触摸结束事件
      this.resumeAutoScroll()
    },
    handleMouseDown() {
      // 鼠标按下事件
      this.isMouseDown = true
      this.pauseAutoScroll()
    },
    handleMouseUp() {
      // 鼠标释放事件
      this.isMouseDown = false
      this.resumeAutoScroll()
    },

    // 获取层级显示文本
    getLevelDisplayText(levelString) {
      return getLevelDisplayText(levelString)
    },

    // 鼠标悬停时暂停滚动
    pauseScrollOnHover() {
      const container = this.$refs.scrollContainer
      if (container && container.classList.contains('css-animation')) {
        container.classList.add('paused')
      }
    },

    // 鼠标离开时恢复滚动
    resumeScrollOnHover() {
      const container = this.$refs.scrollContainer
      if (container) {
        container.classList.remove('paused')
      }
    },

    // 触摸开始时暂停滚动
    pauseScrollOnTouch() {
      const container = this.$refs.scrollContainer
      if (container && container.classList.contains('css-animation')) {
        container.classList.add('paused')
      }
    },

    // 触摸结束时恢复滚动
    resumeScrollOnTouch() {
      const container = this.$refs.scrollContainer
      if (container) {
        container.classList.remove('paused')
      }
    },

    // 动态调整滚动速度
    adjustScrollSpeed(itemDisplayTime = 3) {
      const container = this.$refs.scrollContainer
      if (container && this.rankingData.length > 0) {
        const totalItems = Math.max(this.leftColumnData.length / 2, this.rightColumnData.length / 2)
        const animationDuration = totalItems * itemDisplayTime

        const columns = container.querySelectorAll('.column')
        columns.forEach(column => {
          column.style.animationDuration = animationDuration + 's'
        })

        console.log(`滚动速度已调整: 每个代表显示${itemDisplayTime}秒, 总时长${animationDuration}秒`)
        return animationDuration
      }
      return 0
    },
  },
}
</script>

<style scoped>
.ranking-container {
  width: 100%;
  height: 95%;
  border-radius: 8px;
  padding: 8px;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

.ranking-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: linear-gradient(
      rgba(255, 255, 255, 0.05) 1px,
      transparent 1px
    ),
    linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
  pointer-events: none;
}

.scroll-container {
  display: flex;
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
  z-index: 1;
  gap: 8px;
}

.column {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
}

.left-column {
  margin-right: 4px;
}

.right-column {
  margin-left: 4px;
}

/* CSS动画关键帧 */
@-webkit-keyframes scroll {
  0% { -webkit-transform: translateY(0); }
  100% { -webkit-transform: translateY(-50%); }
}

@keyframes scroll {
  0% { transform: translateY(0); }
  100% { transform: translateY(-50%); }
}

/* 应用CSS动画 */
.css-animation .column {
  -webkit-animation: scroll linear infinite;
  animation: scroll linear infinite;
  /* 动画时长由JavaScript动态设置 */
}

/* 鼠标悬停时暂停动画 */
.css-animation .column:hover {
  -webkit-animation-play-state: paused;
  animation-play-state: paused;
}

/* 暂停状态 */
.css-animation.paused .column {
  -webkit-animation-play-state: paused;
  animation-play-state: paused;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  margin-bottom: 12px;
  min-height: 64px;
}

.ranking-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(5px);
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 12px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  font-size: 16px;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.name {
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.score {
  color: #ffd700;
  font-size: 18px;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 点击效果样式 */
.clickable-representative {
  cursor: pointer;
  transition: all 0.2s ease;
}

.clickable-representative:hover {
  background: rgba(64, 128, 255, 0.1) !important;
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(64, 128, 255, 0.2);
}

/* 代表详情弹窗样式 */
.representative-detail {
  max-width: 500px;
  width: 100%;
  color: #ffffff;
  min-height: 300px;
  box-sizing: border-box;
}

.detail-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid rgba(74, 144, 226, 0.3);
}

.detail-avatar {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 18px;
  flex-shrink: 0;
  border: 3px solid #4a90e2;
  box-shadow: 0 0 15px rgba(74, 144, 226, 0.3);
}

.detail-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.detail-avatar .avatar-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 26px;
}

.detail-name {
  font-size: 26px;
  font-weight: bold;
  color: #4a90e2;
  flex: 1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.detail-body {
  display: grid;
  gap: 18px;
}

.detail-row {
  display: flex;
  align-items: center;
  padding: 14px 0;
  border-bottom: 1px solid rgba(74, 144, 226, 0.2);
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-row .label {
  font-weight: bold;
  min-width: 110px;
  color: #4a90e2;
  font-size: 15px;
}

.detail-row .value {
  flex: 1;
  color: #ffffff;
  font-size: 15px;
  word-break: break-word;
  line-height: 1.5;
}
</style>
